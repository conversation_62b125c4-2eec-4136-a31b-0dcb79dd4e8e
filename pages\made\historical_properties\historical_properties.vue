<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="历史房源" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
				<view class="orderlist_tab">
					<u-tabs lineWidth="55" lineHeight="10" :lineColor="`url(${lineBG1})`" :list="tabOption"
						:disabled="true" :current="current" :scrollable="false" @click="tabClick"
						:activeStyle="{ color: '#006AFC', 'font-size': '28rpx' }"
						:inactiveStyle="{ color: '#1A1A1A', 'font-size': '28rpx' }"
						:itemStyle="{ height: '76rpx', minWidth: '20%' }"></u-tabs>
				</view>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view v-for="(item, index) in list" :key="index">
					<houseItem :info="item"></houseItem>
				</view>

			</view>
			<view slot="bottom">
			</view>
		</z-paging>
	</view>
</template>

<script>
import houseItem from '@/components/common/house_item.vue';

export default {
	components: {
		houseItem
	},
	data() {
		return {
			refresherStatus: 0,
			dataList: [

			],
			list: [{
				id: '1',
				image: 'https://via.placeholder.com/300x200',
				title: '人气好房·东泰未来城国际东门业主精装住宅',
				rooms: '3室2厅',
				area: '90',
				orientation: '朝南',
				district: '晋安府区',
				price: '169',
				pricePerSqm: '18778',
				status: '待审核',
				tags: ['优质户型', '近地铁']
			},
			{
				id: '2',
				image: 'https://via.placeholder.com/300x200',
				title: '人气好房·东泰未来城国际东门业主精装住宅',
				rooms: '3室2厅',
				area: '90',
				orientation: '朝南',
				district: '晋安府区',
				price: '169',
				pricePerSqm: '18778',
				status: '已发布',
				tags: ['优质户型', '全明格局']
			},
			{
				id: '3',
				image: 'https://via.placeholder.com/300x200',
				title: '人气好房·东泰未来城国际东门业主精装住宅',
				rooms: '3室2厅',
				area: '90',
				orientation: '朝南',
				district: '晋安府区',
				price: '169',
				pricePerSqm: '18778',
				status: '已驳回',
				tags: ['有学区']
			}],
			current: 0,
			lineBG1: 'https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/orderlist_icon.png',
		};
	},
	computed: {
		tabOption() {
			let refresherStatus = this.refresherStatus
			return [
				{ name: '全部', val: '', disabled: refresherStatus === 0 ? false : true },
				{ name: '待审核', val: '待审核', disabled: refresherStatus === 0 ? false : true },
				{ name: '已发布', val: '已发布', disabled: refresherStatus === 0 ? false : true },
				{ name: '已驳回', val: '已驳回', disabled: refresherStatus === 0 ? false : true },

			]
		}
	},
	onLoad() { },
	methods: {
		tabClick(val, index) {
			this.current = val.index;
			// this.$refs.paging.reload(true);
		},
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};

			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
		handleActionClick(event) {
			const { action, data } = event;
			console.log('操作点击:', action, data);

			// 根据不同操作执行不同逻辑
			switch (action) {
				case '取消发布':
					this.cancelPublish(data);
					break;
				case '重新发布':
					this.republish(data);
					break;
				case '立即下架':
					this.takeOffline(data);
					break;
				default:
					break;
			}
		},
		cancelPublish(data) {
			uni.showModal({
				title: '确认取消发布',
				content: '确定要取消发布这个房源吗？',
				success: (res) => {
					if (res.confirm) {
						// 执行取消发布逻辑
						console.log('取消发布房源:', data.id);
					}
				}
			});
		},
		republish(data) {
			// 执行重新发布逻辑
			console.log('重新发布房源:', data.id);
		},
		takeOffline(data) {
			uni.showModal({
				title: '确认下架',
				content: '确定要下架这个房源吗？',
				success: (res) => {
					if (res.confirm) {
						// 执行下架逻辑
						console.log('下架房源:', data.id);
					}
				}
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.orderlist_tab {
		width: 100%;

		::v-deep {
			.u-tabs__wrapper__nav__line {
				bottom: 15rpx !important;
				border-radius: 0 !important;
				background-size: contain !important;
				background-repeat: no-repeat !important;
				background-attachment: fixed !important;
				background-position: right !important;
			}
		}
	}

	.loading_list {
		width: 100%;
	}
}
</style>