<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="历史房源" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
				<view class="orderlist_tab">
					<u-tabs lineWidth="55" lineHeight="10" :lineColor="`url(${lineBG1})`" :list="tabOption"
						:disabled="true" :current="current" : @click="tabClick"
						:activeStyle="{ color: '#006AFC', 'font-size': '28rpx' }"
						:inactiveStyle="{ color: '#1A1A1A', 'font-size': '28rpx' }"
						:itemStyle="{ height: '76rpx', minWidth: '20%' }"></u-tabs>
				</view>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">

			</view>
			<view slot="bottom">
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			current: 0,
			lineBG1: 'https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/orderlist_icon.png',
		};
	},
	computed: {
		tabOption() {
			let refresherStatus = this.refresherStatus
			return [
				{ name: '全部', val: '', disabled: refresherStatus === 0 ? false : true },
				{ name: '待审核', val: '待审核', disabled: refresherStatus === 0 ? false : true },
				{ name: '已发布', val: '已发布', disabled: refresherStatus === 0 ? false : true },
				{ name: '已驳回', val: '已驳回', disabled: refresherStatus === 0 ? false : true },

			]
		}
	},
	onLoad() { },
	methods: {
		tabClick(val, index) {
			this.current = val.index;
			// this.$refs.paging.reload(true);
		},
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false" 
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.orderlist_tab {
		width: 100%;

		::v-deep {
			.u-tabs__wrapper__nav__line {
				bottom: 15rpx !important;
				border-radius: 0 !important;
				background-size: contain !important;
				background-repeat: no-repeat !important;
				background-attachment: fixed !important;
				background-position: right !important;
			}
		}
	}

	.loading_list {
		width: 100%;
	}
}
</style>